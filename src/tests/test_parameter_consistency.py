"""
测试参数一致性 - 验证UI调整的参数与实际运行时使用的参数完全一致
"""

import unittest
import json
import tempfile
import os
from pathlib import Path
import sys

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import ScanConfig
from project_manager import ProjectManager, ProjectConfig
from grid_executor import GridExecutor


class TestParameterConsistency(unittest.TestCase):
    """测试参数一致性"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_manager = ProjectManager(self.temp_dir)
        
        # 创建测试项目
        self.test_project_name = "test_consistency"
        self.project_dir = self.project_manager.create_project(
            name=self.test_project_name,
            description="参数一致性测试项目",
            center_lat=34.0522,
            center_lng=-118.2437,
            scan_radius=25.0
        )
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_h3_resolution_consistency(self):
        """测试H3分辨率参数一致性"""
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 创建ScanConfig（跳过系统检查）
        scan_config = ScanConfig(skip_system_checks=True, mock_mode=True)
        
        # 验证H3分辨率参数一致性
        self.assertEqual(
            project_config.h3_res_level1, 
            scan_config.H3_RES_LEVEL1,
            "ProjectConfig和ScanConfig的H3_RES_LEVEL1不一致"
        )
        
        self.assertEqual(
            project_config.h3_res_level2, 
            scan_config.H3_RES_LEVEL2,
            "ProjectConfig和ScanConfig的H3_RES_LEVEL2不一致"
        )
        
        self.assertEqual(
            project_config.h3_res_level3, 
            scan_config.H3_RES_LEVEL3,
            "ProjectConfig和ScanConfig的H3_RES_LEVEL3不一致"
        )
    
    def test_search_radius_consistency(self):
        """测试搜索半径参数一致性"""
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 创建ScanConfig（跳过系统检查）
        scan_config = ScanConfig(skip_system_checks=True, mock_mode=True)
        
        # 验证搜索半径参数一致性（都应该是米为单位）
        self.assertEqual(
            project_config.search_radius_level1, 
            scan_config.SEARCH_RADIUS_LEVEL1,
            "ProjectConfig和ScanConfig的SEARCH_RADIUS_LEVEL1不一致"
        )
        
        self.assertEqual(
            project_config.search_radius_level2, 
            scan_config.SEARCH_RADIUS_LEVEL2,
            "ProjectConfig和ScanConfig的SEARCH_RADIUS_LEVEL2不一致"
        )
        
        self.assertEqual(
            project_config.search_radius_level3, 
            scan_config.SEARCH_RADIUS_LEVEL3,
            "ProjectConfig和ScanConfig的SEARCH_RADIUS_LEVEL3不一致"
        )
    
    def test_trigger_count_consistency(self):
        """测试触发阈值参数一致性"""
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 创建ScanConfig（跳过系统检查）
        scan_config = ScanConfig(skip_system_checks=True, mock_mode=True)
        
        # 验证触发阈值参数一致性
        self.assertEqual(
            project_config.recursion_trigger_count, 
            scan_config.RECURSION_TRIGGER_COUNT,
            "ProjectConfig和ScanConfig的recursion_trigger_count不一致"
        )
    
    def test_grid_executor_config_consistency(self):
        """测试GridExecutor配置一致性"""
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 创建GridExecutor
        executor = GridExecutor(self.test_project_name, self.project_manager, mock_mode=True)
        
        # 获取内部的scan_config
        scan_config = executor._create_scan_config()
        
        # 验证GridExecutor使用的配置与ProjectConfig一致
        self.assertEqual(
            project_config.h3_res_level1,
            scan_config.H3_RES_LEVEL1,
            "GridExecutor的H3_RES_LEVEL1与ProjectConfig不一致"
        )
        
        self.assertEqual(
            project_config.search_radius_level1,
            scan_config.SEARCH_RADIUS_LEVEL1,
            "GridExecutor的SEARCH_RADIUS_LEVEL1与ProjectConfig不一致"
        )
        
        self.assertEqual(
            project_config.recursion_trigger_count,
            scan_config.RECURSION_TRIGGER_COUNT,
            "GridExecutor的RECURSION_TRIGGER_COUNT与ProjectConfig不一致"
        )
    
    def test_ui_config_format_consistency(self):
        """测试UI配置格式一致性"""
        # 模拟从UI导出的配置格式
        ui_config = {
            "h3_res_level1": 5,
            "h3_res_level2": 7,
            "h3_res_level3": 8,
            "search_radius_level1": 5000,  # 米
            "search_radius_level2": 1000,  # 米
            "search_radius_level3": 500,   # 米
            "recursion_trigger_count": 20,
            "api_cost_per_call": 0.032,
            "max_budget": 200.0
        }
        
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 验证UI配置格式与ProjectConfig字段名一致
        self.assertEqual(ui_config["h3_res_level1"], project_config.h3_res_level1)
        self.assertEqual(ui_config["h3_res_level2"], project_config.h3_res_level2)
        self.assertEqual(ui_config["h3_res_level3"], project_config.h3_res_level3)
        self.assertEqual(ui_config["search_radius_level1"], project_config.search_radius_level1)
        self.assertEqual(ui_config["search_radius_level2"], project_config.search_radius_level2)
        self.assertEqual(ui_config["search_radius_level3"], project_config.search_radius_level3)
        self.assertEqual(ui_config["recursion_trigger_count"], project_config.recursion_trigger_count)
    
    def test_search_radius_units(self):
        """测试搜索半径单位一致性（都应该是米）"""
        # 加载项目配置
        _, project_config = self.project_manager.load_project(self.test_project_name)
        
        # 验证所有搜索半径都是合理的米值（不是公里值）
        self.assertGreaterEqual(project_config.search_radius_level1, 1000, 
                               "search_radius_level1应该是米为单位，不应该小于1000")
        self.assertLessEqual(project_config.search_radius_level1, 50000, 
                            "search_radius_level1不应该超过50000米（Google API限制）")
        
        self.assertGreaterEqual(project_config.search_radius_level2, 100, 
                               "search_radius_level2应该是米为单位")
        self.assertLessEqual(project_config.search_radius_level2, 10000, 
                            "search_radius_level2应该小于Level1")
        
        self.assertGreaterEqual(project_config.search_radius_level3, 50, 
                               "search_radius_level3应该是米为单位")
        self.assertLessEqual(project_config.search_radius_level3, 5000, 
                            "search_radius_level3应该小于Level2")


if __name__ == '__main__':
    unittest.main()
